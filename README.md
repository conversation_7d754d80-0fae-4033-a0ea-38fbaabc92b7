# 🎬 WatchIt

A comprehensive movie and TV tracking application with real-time theater showtimes, streaming availability, and user reviews. Built with Next.js, TypeScript, and integrated with multiple entertainment APIs.

## ✨ Features

### 🎭 **Core Functionality**
- **Infinite Scroll Feed** - Discover trending movies and TV shows
- **Detailed Pages** - Comprehensive information for movies and TV series
- **Real-time Data** - Live updates from The Movie Database (TMDb)

### 🎪 **Theater Integration**
- **AMC Showtimes** - Real theater showtimes with geolocation
- **Theater Details** - Names, addresses, and contact information
- **Direct Ticket Links** - One-click purchasing through AMC
- **Premium Formats** - Dolby Cinema, IMAX, and special screenings

### 📺 **Streaming & Media**
- **Watch Providers** - Netflix, Disney+, Amazon Prime, and more
- **Trailer Integration** - YouTube trailers with thumbnail previews
- **Reviews System** - TMDb user reviews and ratings
- **Similar Content** - Personalized recommendations

### 🔧 **Technical Features**
- **Responsive Design** - Mobile-first with Tailwind CSS
- **Type Safety** - Full TypeScript implementation
- **Modern UI** - shadcn/ui component library
- **Performance** - TanStack Query for efficient data fetching
- **SEO Optimized** - Next.js App Router with metadata

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: TanStack Query
- **Database**: Supabase (configured)
- **APIs**: TMDb, AMC Theatres, YouTube
- **Deployment**: Vercel-ready

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/kcarriedo/watchit.git
   cd watchit
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

4. **Configure API Keys**
   ```env
   # TMDb API
   NEXT_PUBLIC_TMDB_API_KEY=your_tmdb_api_key
   TMDB_ACCESS_TOKEN=your_tmdb_access_token

   # AMC Theatres API
   NEXT_PUBLIC_AMC_API_KEY=your_amc_api_key

   # Supabase (optional)
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Run Development Server**
   ```bash
   npm run dev
   ```

## 📱 Usage

### **Discover Content**
- Browse trending movies and TV shows on the homepage
- Infinite scroll for continuous discovery
- Click any poster to view detailed information

### **Movie/TV Details**
- View comprehensive information including cast, crew, and synopsis
- Watch trailers with high-quality thumbnails
- Read user reviews and ratings
- Find similar content recommendations

### **Theater Showtimes**
- Click "Find Theaters Near Me" on movie detail pages
- Allow location access for nearby AMC theaters
- View real showtimes with premium format information
- Click any showtime to purchase tickets directly

### **Streaming Options**
- See where content is available to stream, rent, or purchase
- Direct links to streaming platforms
- Organized by availability type (streaming, rental, purchase)

## 🔑 API Keys Required

1. **TMDb API** - [Get API Key](https://www.themoviedb.org/settings/api)
2. **AMC Theatres API** - Contact AMC for vendor key
3. **Supabase** (optional) - [Create Project](https://supabase.com)

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── api/            # API routes (AMC proxy)
│   ├── details/        # Movie/TV detail pages
│   └── globals.css     # Global styles
├── components/         # React components
│   ├── ui/            # shadcn/ui components
│   └── ...            # Feature components
└── lib/               # Utilities and API clients
    ├── tmdb.ts        # TMDb API client
    ├── amc.ts         # AMC API client
    └── utils.ts       # Utility functions
```

## 🚀 Deployment

The application is optimized for Vercel deployment:

```bash
npm run build
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **TMDb** - Movie and TV data
- **AMC Theatres** - Theater showtimes
- **shadcn/ui** - Component library
- **Vercel** - Deployment platform
