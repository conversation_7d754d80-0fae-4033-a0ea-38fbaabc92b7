'use client'

import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { watchlistService } from '@/lib/watchlist'
import { useAuth } from '@/lib/auth-context'
import { toast } from 'sonner'
import { Loader2, Plus } from 'lucide-react'

interface CreateListModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (listId: string) => void
}

export function CreateListModal({ open, onOpenChange, onSuccess }: CreateListModalProps) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isPublic, setIsPublic] = useState(false)
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const createListMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('Not authenticated')
      if (!name.trim()) throw new Error('List name is required')

      return watchlistService.createList(
        user.id,
        name.trim(),
        description.trim() || undefined,
        isPublic
      )
    },
    onSuccess: (newList) => {
      if (newList) {
        toast.success('List created successfully!')
        queryClient.invalidateQueries({ queryKey: ['user-lists', user?.id] })
        onSuccess?.(newList.id)
        resetForm()
        onOpenChange(false)
      } else {
        toast.error('Failed to create list')
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create list')
    }
  })

  const resetForm = () => {
    setName('')
    setDescription('')
    setIsPublic(false)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    createListMutation.mutate()
  }

  const handleClose = () => {
    resetForm()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New List
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="list-name">List Name *</Label>
            <Input
              id="list-name"
              type="text"
              placeholder="e.g., Movies to Watch This Weekend"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="list-description">Description (Optional)</Label>
            <Textarea
              id="list-description"
              placeholder="What's this list about?"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              maxLength={500}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="list-public">Make this list public</Label>
              <p className="text-xs text-muted-foreground">
                Other users can discover and view your list
              </p>
            </div>
            <Switch
              id="list-public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createListMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createListMutation.isPending || !name.trim()}
            >
              {createListMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Create List
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
