'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Star, ThumbsUp, MessageSquare, ChevronDown, ChevronUp } from 'lucide-react'
import { TMDbReview } from '@/lib/tmdb'

interface ReviewsSectionProps {
  reviews: TMDbReview[]
  isLoading?: boolean
}

export function ReviewsSection({ reviews, isLoading }: ReviewsSectionProps) {
  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(new Set())

  const toggleReview = (reviewId: string) => {
    const newExpanded = new Set(expandedReviews)
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId)
    } else {
      newExpanded.add(reviewId)
    }
    setExpandedReviews(newExpanded)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getAvatarUrl = (avatarPath: string | null) => {
    if (!avatarPath) return null
    if (avatarPath.startsWith('/https://')) {
      return avatarPath.substring(1) // Remove leading slash for external URLs
    }
    return `https://image.tmdb.org/t/p/w64_and_h64_face${avatarPath}`
  }

  const truncateText = (text: string, maxLength: number = 300) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Reviews</h3>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-muted rounded-full" />
                  <div className="space-y-1">
                    <div className="h-4 bg-muted rounded w-24" />
                    <div className="h-3 bg-muted rounded w-16" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded w-full" />
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-4 bg-muted rounded w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Reviews</h3>
        <Card>
          <CardContent className="py-8 text-center">
            <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No reviews available yet.</p>
            <p className="text-sm text-muted-foreground mt-1">
              Be the first to share your thoughts!
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Reviews</h3>
        <Badge variant="secondary">
          {reviews.length} review{reviews.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="space-y-4">
        {reviews.slice(0, 5).map((review) => {
          const isExpanded = expandedReviews.has(review.id)
          const shouldTruncate = review.content.length > 300
          const displayContent = isExpanded || !shouldTruncate 
            ? review.content 
            : truncateText(review.content)

          return (
            <Card key={review.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage 
                        src={getAvatarUrl(review.author_details.avatar_path) || undefined}
                        alt={review.author_details.name || review.author}
                      />
                      <AvatarFallback>
                        {(review.author_details.name || review.author).charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">
                        {review.author_details.name || review.author}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{formatDate(review.created_at)}</span>
                        {review.author_details.rating && (
                          <>
                            <span>•</span>
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 fill-yellow-500 text-yellow-500" />
                              <span>{review.author_details.rating}/10</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {review.author_details.rating && (
                    <Badge 
                      variant={review.author_details.rating >= 7 ? "default" : 
                               review.author_details.rating >= 5 ? "secondary" : "destructive"}
                      className="ml-2"
                    >
                      {review.author_details.rating}/10
                    </Badge>
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-wrap text-sm leading-relaxed">
                      {displayContent}
                    </p>
                  </div>
                  
                  {shouldTruncate && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleReview(review.id)}
                      className="h-auto p-0 text-primary hover:text-primary/80"
                    >
                      {isExpanded ? (
                        <>
                          <ChevronUp className="w-4 h-4 mr-1" />
                          Show less
                        </>
                      ) : (
                        <>
                          <ChevronDown className="w-4 h-4 mr-1" />
                          Read more
                        </>
                      )}
                    </Button>
                  )}
                  
                  <div className="flex items-center gap-4 pt-2 border-t">
                    <Button variant="ghost" size="sm" className="h-auto p-0">
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      Helpful
                    </Button>
                    <Button variant="ghost" size="sm" className="h-auto p-0">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Reply
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {reviews.length > 5 && (
        <div className="text-center">
          <Button variant="outline">
            Load More Reviews
          </Button>
        </div>
      )}
    </div>
  )
}
