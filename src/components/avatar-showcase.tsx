'use client'

import { UserAvatar } from './user-avatar'

// Demo component to showcase different avatar styles
export function AvatarShowcase() {
  const demoUsers = [
    { id: 'user1', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user2', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user3', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user4', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user5', name: '<PERSON>', email: '<EMAIL>' },
    { id: 'user6', name: '<PERSON>', email: '<EMAIL>' },
  ]

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Avatar Showcase</h2>
        <p className="text-muted-foreground mb-6">
          Beautiful, consistent avatars with smart defaults and colorful fallbacks
        </p>
      </div>

      {/* Size Variations */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Size Variations</h3>
        <div className="flex items-center gap-4">
          <div className="text-center">
            <UserAvatar
              userId="demo-sm"
              displayName="Small"
              email="<EMAIL>"
              size="sm"
            />
            <p className="text-xs mt-2">Small</p>
          </div>
          <div className="text-center">
            <UserAvatar
              userId="demo-md"
              displayName="Medium"
              email="<EMAIL>"
              size="md"
            />
            <p className="text-xs mt-2">Medium</p>
          </div>
          <div className="text-center">
            <UserAvatar
              userId="demo-lg"
              displayName="Large"
              email="<EMAIL>"
              size="lg"
            />
            <p className="text-xs mt-2">Large</p>
          </div>
          <div className="text-center">
            <UserAvatar
              userId="demo-xl"
              displayName="Extra Large"
              email="<EMAIL>"
              size="xl"
            />
            <p className="text-xs mt-2">Extra Large</p>
          </div>
        </div>
      </div>

      {/* Color Variations */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Color Variations</h3>
        <div className="grid grid-cols-6 gap-4">
          {demoUsers.map((user) => (
            <div key={user.id} className="text-center">
              <UserAvatar
                userId={user.id}
                displayName={user.name}
                email={user.email}
                size="lg"
              />
              <p className="text-xs mt-2 font-medium">{user.name}</p>
            </div>
          ))}
        </div>
      </div>

      {/* With Border */}
      <div>
        <h3 className="text-lg font-semibold mb-4">With Border</h3>
        <div className="flex items-center gap-4">
          {demoUsers.slice(0, 3).map((user) => (
            <UserAvatar
              key={user.id}
              userId={user.id}
              displayName={user.name}
              email={user.email}
              size="lg"
              showBorder
            />
          ))}
        </div>
      </div>
    </div>
  )
}
