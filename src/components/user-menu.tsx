'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { AuthModal } from './auth-modal'
import { UserAvatar } from './user-avatar'
import { useAuth } from '@/lib/auth-context'
import { getDisplayName } from '@/lib/avatar-utils'
import { User, Settings, Heart, List, LogOut, Film } from 'lucide-react'
import Link from 'next/link'

export function UserMenu() {
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const { user, profile, signOut, loading } = useAuth()

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
      </div>
    )
  }

  if (!user) {
    return (
      <>
        <Button 
          variant="outline" 
          onClick={() => setAuthModalOpen(true)}
          className="hidden sm:flex"
        >
          Sign In
        </Button>
        <Button 
          size="sm"
          onClick={() => setAuthModalOpen(true)}
          className="sm:hidden"
        >
          Sign In
        </Button>
        <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />
      </>
    )
  }

  const displayName = getDisplayName(
    profile?.display_name,
    profile?.username,
    user.email
  )

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0">
          <UserAvatar
            userId={user.id}
            displayName={profile?.display_name}
            username={profile?.username}
            email={user.email}
            avatarUrl={profile?.avatar_url}
            size="md"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex items-center space-x-3 py-2">
            <UserAvatar
              userId={user.id}
              displayName={profile?.display_name}
              username={profile?.username}
              email={user.email}
              avatarUrl={profile?.avatar_url}
              size="lg"
            />
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{displayName}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Heart className="mr-2 h-4 w-4" />
          <span>Liked Movies</span>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/lists">
            <List className="mr-2 h-4 w-4" />
            <span>My Lists</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Film className="mr-2 h-4 w-4" />
          <span>My Reviews</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={signOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
