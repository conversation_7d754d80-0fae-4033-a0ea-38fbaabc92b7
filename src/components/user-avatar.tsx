'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getAvatarProps } from '@/lib/avatar-utils'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  userId: string
  displayName?: string | null
  username?: string | null
  email?: string | null
  avatarUrl?: string | null
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  showBorder?: boolean
}

const sizeClasses = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-10 w-10',
  xl: 'h-12 w-12'
}

const textSizeClasses = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
  xl: 'text-lg'
}

export function UserAvatar({
  userId,
  displayName,
  username,
  email,
  avatarUrl,
  size = 'md',
  className,
  showBorder = false
}: UserAvatarProps) {
  const avatarProps = getAvatarProps(userId, displayName, username, email, avatarUrl)

  return (
    <Avatar
      className={cn(
        sizeClasses[size],
        showBorder && 'ring-2 ring-background ring-offset-2',
        'transition-all duration-200 hover:scale-105',
        className
      )}
    >
      <AvatarImage
        src={avatarProps.avatarUrl}
        alt={avatarProps.name}
        className="object-cover transition-all duration-200"
      />
      <AvatarFallback
        className={cn(
          avatarProps.fallbackProps.className,
          textSizeClasses[size],
          'transition-all duration-200 hover:brightness-110'
        )}
      >
        {avatarProps.initials}
      </AvatarFallback>
    </Avatar>
  )
}
