'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { CreateListModal } from './create-list-modal'
import { watchlistService } from '@/lib/watchlist'
import { useAuth } from '@/lib/auth-context'
import { toast } from 'sonner'
import { Plus, List as ListIcon, Loader2, Check } from 'lucide-react'
import { List } from '@/lib/supabase'

interface WatchlistPickerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tmdbData: {
    id: number
    media_type: 'movie' | 'tv'
    title?: string
    name?: string
    release_date?: string
    first_air_date?: string
    poster_path?: string
    backdrop_path?: string
    overview?: string
  }
}

export function WatchlistPicker({ open, onOpenChange, tmdbData }: WatchlistPickerProps) {
  const [createListOpen, setCreateListOpen] = useState(false)
  const [selectedLists, setSelectedLists] = useState<Set<string>>(new Set())
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const title = tmdbData.title || tmdbData.name || 'Unknown Title'

  // Get user's lists
  const { data: userLists = [], isLoading: listsLoading } = useQuery({
    queryKey: ['user-lists', user?.id],
    queryFn: () => user ? watchlistService.getUserLists(user.id) : [],
    enabled: !!user && open,
  })

  // Get which lists already contain this title
  const { data: existingLists = [] } = useQuery({
    queryKey: ['title-lists', tmdbData.id, tmdbData.media_type, user?.id],
    queryFn: async () => {
      if (!user) return []
      
      // First get or create the title
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) return []

      // Then get lists containing this title
      return watchlistService.getListsContainingTitle(title.id, user.id)
    },
    enabled: !!user && open,
  })

  // Initialize selected lists with existing ones
  useState(() => {
    const existingListIds = new Set(existingLists.map(list => list.id))
    setSelectedLists(existingListIds)
  })

  const addToListsMutation = useMutation({
    mutationFn: async (listIds: string[]) => {
      if (!user) throw new Error('Not authenticated')
      
      // Get or create the title first
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) throw new Error('Failed to create title')

      // Add to selected lists and remove from unselected ones
      const results = await Promise.all([
        ...listIds.map(listId => watchlistService.addToList(listId, title.id)),
        ...existingLists
          .filter(list => !listIds.includes(list.id))
          .map(list => watchlistService.removeFromList(list.id, title.id))
      ])

      return results.every(result => result)
    },
    onSuccess: (success) => {
      if (success) {
        toast.success('Lists updated successfully!')
        queryClient.invalidateQueries({ queryKey: ['title-lists', tmdbData.id, tmdbData.media_type, user?.id] })
        queryClient.invalidateQueries({ queryKey: ['user-lists', user?.id] })
        onOpenChange(false)
      } else {
        toast.error('Failed to update lists')
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update lists')
    }
  })

  const handleListToggle = (listId: string, checked: boolean) => {
    const newSelected = new Set(selectedLists)
    if (checked) {
      newSelected.add(listId)
    } else {
      newSelected.delete(listId)
    }
    setSelectedLists(newSelected)
  }

  const handleSave = () => {
    addToListsMutation.mutate(Array.from(selectedLists))
  }

  const handleCreateListSuccess = (newListId: string) => {
    setSelectedLists(prev => new Set([...prev, newListId]))
    queryClient.invalidateQueries({ queryKey: ['user-lists', user?.id] })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ListIcon className="h-5 w-5" />
              Add "{title}" to Lists
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {listsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : userLists.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">You don't have any lists yet.</p>
                <Button onClick={() => setCreateListOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First List
                </Button>
              </div>
            ) : (
              <>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {userLists.map((list) => (
                    <div key={list.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted">
                      <Checkbox
                        id={list.id}
                        checked={selectedLists.has(list.id)}
                        onCheckedChange={(checked) => handleListToggle(list.id, !!checked)}
                      />
                      <label
                        htmlFor={list.id}
                        className="flex-1 cursor-pointer"
                      >
                        <div className="font-medium">{list.name}</div>
                        {list.description && (
                          <div className="text-sm text-muted-foreground">{list.description}</div>
                        )}
                        {list.is_default && (
                          <div className="text-xs text-blue-600">Default Watchlist</div>
                        )}
                      </label>
                    </div>
                  ))}
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setCreateListOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create New List
                </Button>
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={addToListsMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={addToListsMutation.isPending || userLists.length === 0}
              >
                {addToListsMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                <Check className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <CreateListModal
        open={createListOpen}
        onOpenChange={setCreateListOpen}
        onSuccess={handleCreateListSuccess}
      />
    </>
  )
}
