'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Play, ExternalLink } from 'lucide-react'
import Image from 'next/image'
import { TMDbVideo } from '@/lib/tmdb'

interface TrailerPlayerProps {
  videos: TMDbVideo[]
  title: string
}

export function TrailerPlayer({ videos, title }: TrailerPlayerProps) {
  const [selectedVideo, setSelectedVideo] = useState<TMDbVideo | null>(null)

  // Filter for YouTube trailers and teasers, prioritize official ones
  const trailers = videos
    .filter(video => 
      video.site === 'YouTube' && 
      (video.type === 'Trailer' || video.type === 'Teaser')
    )
    .sort((a, b) => {
      // Prioritize official trailers
      if (a.official && !b.official) return -1
      if (!a.official && b.official) return 1
      // Then prioritize trailers over teasers
      if (a.type === 'Trailer' && b.type === 'Teaser') return -1
      if (a.type === 'Teaser' && b.type === 'Trailer') return 1
      return 0
    })

  if (trailers.length === 0) {
    return null
  }

  const mainTrailer = trailers[0]

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Trailers & Videos</h3>
      
      {/* Main Trailer */}
      <Dialog>
        <DialogTrigger asChild>
          <div className="relative group cursor-pointer rounded-lg overflow-hidden">
            <div className="aspect-video relative">
              <Image
                src={`https://img.youtube.com/vi/${mainTrailer.key}/maxresdefault.jpg`}
                alt={mainTrailer.name}
                fill
                className="object-cover"
                onError={(e) => {
                  // Fallback to medium quality thumbnail if maxres doesn't exist
                  e.currentTarget.src = `https://img.youtube.com/vi/${mainTrailer.key}/hqdefault.jpg`
                }}
              />
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-20 h-20 bg-red-600 rounded-full flex items-center justify-center group-hover:bg-red-500 transition-colors shadow-lg">
                  <Play className="w-10 h-10 text-white fill-white ml-1" />
                </div>
              </div>
              <div className="absolute bottom-4 left-4 right-4">
                <p className="font-medium text-white text-lg drop-shadow-lg">{mainTrailer.name}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs">
                    {mainTrailer.type}
                  </Badge>
                  {mainTrailer.official && (
                    <Badge variant="default" className="text-xs">
                      Official
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </DialogTrigger>
        <DialogContent className="max-w-6xl w-[95vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{mainTrailer.name}</DialogTitle>
          </DialogHeader>
          <div className="aspect-video">
            <iframe
              src={`https://www.youtube.com/embed/${mainTrailer.key}?autoplay=1&rel=0&modestbranding=1`}
              title={mainTrailer.name}
              className="w-full h-full rounded-lg"
              allowFullScreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Additional Trailers */}
      {trailers.length > 1 && (
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">More Videos</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {trailers.slice(1, 5).map((video) => (
              <Dialog key={video.id}>
                <DialogTrigger asChild>
                  <div className="relative group cursor-pointer rounded-lg overflow-hidden border hover:border-primary/50 transition-colors">
                    <div className="aspect-video relative">
                      <Image
                        src={`https://img.youtube.com/vi/${video.key}/hqdefault.jpg`}
                        alt={video.name}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-12 h-12 bg-red-600/90 rounded-full flex items-center justify-center group-hover:bg-red-500 transition-colors">
                          <Play className="w-6 h-6 text-white fill-white ml-0.5" />
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-background">
                      <p className="font-medium text-sm truncate">{video.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          {video.type}
                        </Badge>
                        {video.official && (
                          <Badge variant="default" className="text-xs">
                            Official
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-6xl w-[95vw] max-h-[90vh]">
                  <DialogHeader>
                    <DialogTitle>{video.name}</DialogTitle>
                  </DialogHeader>
                  <div className="aspect-video">
                    <iframe
                      src={`https://www.youtube.com/embed/${video.key}?autoplay=1&rel=0&modestbranding=1`}
                      title={video.name}
                      className="w-full h-full rounded-lg"
                      allowFullScreen
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
