'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ExternalLink, Play, ShoppingCart, DollarSign, Tv } from 'lucide-react'
import { TMDbWatchProviders, TMDbWatchProvider, tmdbClient } from '@/lib/tmdb'
import Image from 'next/image'

interface WatchProvidersProps {
  watchProviders: TMDbWatchProviders
  isLoading?: boolean
  title: string
}

export function WatchProviders({ watchProviders, isLoading, title }: WatchProvidersProps) {
  // Focus on US providers for now, but could be made configurable
  const countryCode = 'US'
  const providers = watchProviders?.results?.[countryCode]

  const getProviderImageUrl = (logoPath: string) => {
    return `https://image.tmdb.org/t/p/w92${logoPath}`
  }

  const ProviderSection = ({ 
    title: sectionTitle, 
    providers: sectionProviders, 
    icon: Icon,
    color = "default" as const
  }: {
    title: string
    providers: TMDbWatchProvider[]
    icon: React.ComponentType<{ className?: string }>
    color?: "default" | "secondary" | "destructive" | "outline"
  }) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Icon className="w-4 h-4" />
        <h4 className="font-medium text-sm">{sectionTitle}</h4>
        <Badge variant={color} className="text-xs">
          {sectionProviders.length}
        </Badge>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {sectionProviders
          .sort((a, b) => a.display_priority - b.display_priority)
          .slice(0, 8)
          .map((provider) => (
            <div
              key={provider.provider_id}
              className="flex flex-col items-center p-3 rounded-lg border hover:border-primary/50 transition-colors cursor-pointer group"
            >
              <div className="w-12 h-12 relative mb-2 rounded-lg overflow-hidden">
                <Image
                  src={getProviderImageUrl(provider.logo_path)}
                  alt={provider.provider_name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform"
                />
              </div>
              <p className="text-xs text-center font-medium line-clamp-2">
                {provider.provider_name}
              </p>
            </div>
          ))}
      </div>
    </div>
  )

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tv className="w-5 h-5" />
            Where to Watch
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-muted rounded animate-pulse" />
                  <div className="h-4 bg-muted rounded w-20 animate-pulse" />
                  <div className="h-5 bg-muted rounded w-8 animate-pulse" />
                </div>
                <div className="grid grid-cols-4 gap-3">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="space-y-2">
                      <div className="w-12 h-12 bg-muted rounded-lg animate-pulse" />
                      <div className="h-3 bg-muted rounded animate-pulse" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!providers) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tv className="w-5 h-5" />
            Where to Watch
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Tv className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No streaming information available for your region.
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Check back later or try a different region.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const hasAnyProviders = providers.flatrate?.length || providers.rent?.length || providers.buy?.length || providers.free?.length

  if (!hasAnyProviders) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tv className="w-5 h-5" />
            Where to Watch
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Tv className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              "{title}" is not currently available for streaming, rental, or purchase.
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Check back later for availability updates.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tv className="w-5 h-5" />
            Where to Watch
          </div>
          <Button variant="outline" size="sm" asChild>
            <a 
              href={providers.link} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-1"
            >
              <ExternalLink className="w-3 h-3" />
              JustWatch
            </a>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {providers.flatrate && providers.flatrate.length > 0 && (
            <ProviderSection
              title="Stream"
              providers={providers.flatrate}
              icon={Play}
              color="default"
            />
          )}

          {providers.free && providers.free.length > 0 && (
            <ProviderSection
              title="Free with Ads"
              providers={providers.free}
              icon={Tv}
              color="secondary"
            />
          )}

          {providers.rent && providers.rent.length > 0 && (
            <ProviderSection
              title="Rent"
              providers={providers.rent}
              icon={DollarSign}
              color="outline"
            />
          )}

          {providers.buy && providers.buy.length > 0 && (
            <ProviderSection
              title="Buy"
              providers={providers.buy}
              icon={ShoppingCart}
              color="outline"
            />
          )}
        </div>

        <div className="mt-6 pt-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Streaming data provided by{' '}
            <a 
              href="https://www.justwatch.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              JustWatch
            </a>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
