'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MapPin, Clock, ExternalLink, Film, AlertCircle, Loader2 } from 'lucide-react'
import { useGeolocation } from '@/lib/geolocation'
import { amcClient, AMCShowtimesResponse, AMCShowtime, AMCTheatersResponse, AMCTheater } from '@/lib/amc'

interface MovieShowtimesProps {
  movieTitle: string
  isMovie: boolean // Only show for movies, not TV shows
}

export function MovieShowtimes({ movieTitle, isMovie }: MovieShowtimesProps) {
  const [hasRequestedLocation, setHasRequestedLocation] = useState(false)
  const { latitude, longitude, error, loading, permission, requestLocation } = useGeolocation()

  // Only fetch showtimes if we have location and it's a movie
  const {
    data: showtimes,
    isLoading: showtimesLoading,
    error: showtimesError
  } = useQuery({
    queryKey: ['amc-showtimes', movieTitle, latitude, longitude],
    queryFn: () => amcClient.getShowtimesByTitle(movieTitle, latitude!, longitude!),
    enabled: isMovie && !!latitude && !!longitude && hasRequestedLocation,
    staleTime: 1000 * 60 * 30, // 30 minutes
  })

  // Get unique theater IDs from showtimes
  const theaterIds = showtimes?._embedded?.showtimes
    ? Array.from(new Set(showtimes._embedded.showtimes.map(s => s.theatreId)))
    : []

  const { data: theaters, isLoading: theatersLoading } = useQuery({
    queryKey: ['amc-theaters', theaterIds],
    queryFn: async () => {
      if (theaterIds.length === 0) return null
      return amcClient.getTheaters(theaterIds) as Promise<AMCTheatersResponse>
    },
    enabled: theaterIds.length > 0,
    staleTime: 30 * 60 * 1000, // 30 minutes
  })

  const handleRequestLocation = () => {
    setHasRequestedLocation(true)
    requestLocation()
  }

  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString)
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return timeString
    }
  }

  const formatDistance = (distance: number) => {
    return `${distance.toFixed(1)} mi`
  }

  // Group showtimes by theater
  const groupShowtimesByTheater = (showtimes: AMCShowtime[]) => {
    const grouped = showtimes.reduce((acc, showtime) => {
      const theaterId = showtime.theatreId
      if (!acc[theaterId]) {
        acc[theaterId] = []
      }
      acc[theaterId].push(showtime)
      return acc
    }, {} as Record<number, AMCShowtime[]>)

    return Object.entries(grouped).map(([theaterId, showtimes]) => ({
      theaterId: parseInt(theaterId),
      showtimes: showtimes.sort((a, b) =>
        new Date(a.showDateTimeLocal).getTime() - new Date(b.showDateTimeLocal).getTime()
      )
    }))
  }

  // Get theater name by ID
  const getTheaterName = (theaterId: number): string => {
    const theater = theaters?._embedded?.theatres?.find(t => t.id === theaterId)
    return theater?.name || `AMC Theater #${theaterId}`
  }

  // Get theater address by ID
  const getTheaterAddress = (theaterId: number): string => {
    const theater = theaters?._embedded?.theatres?.find(t => t.id === theaterId)
    if (theater?.location) {
      const { addressLine1, city, state } = theater.location
      if (addressLine1 && city && state) {
        return `${addressLine1}, ${city}, ${state}`
      }
      if (city && state) {
        return `${city}, ${state}`
      }
    }
    if (theater) {
      return `Theater ID: ${theaterId}`
    }
    return 'Loading location...'
  }

  // Get short theater address (city, state only)
  const getTheaterShortAddress = (theaterId: number): string => {
    const theater = theaters?._embedded?.theatres?.find(t => t.id === theaterId)
    if (theater?.location?.city && theater?.location?.state) {
      return `${theater.location.city}, ${theater.location.state}`
    }
    if (theater) {
      return `Theater ID: ${theaterId}`
    }
    return 'Loading location...'
  }

  // Don't show for TV shows
  if (!isMovie) {
    return null
  }

  // Initial state - show location request
  if (!hasRequestedLocation) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">Find Nearby Theaters</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Allow location access to see AMC theater showtimes near you
            </p>
            <Button onClick={handleRequestLocation} className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Find Theaters Near Me
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Loading location
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Loader2 className="w-8 h-8 text-primary mx-auto mb-4 animate-spin" />
            <p className="text-sm text-muted-foreground">Getting your location...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Location error
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
          {permission === 'denied' && (
            <div className="mt-4 text-center">
              <p className="text-xs text-muted-foreground">
                To enable location access, please check your browser settings
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // Loading showtimes or theaters
  if (showtimesLoading || theatersLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-muted rounded animate-pulse" />
                  <div className="h-4 bg-muted rounded w-32 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-16 animate-pulse" />
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="h-8 bg-muted rounded animate-pulse" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // No showtimes found
  if (!showtimes || !showtimes._embedded?.showtimes || showtimes._embedded.showtimes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Film className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">No Showtimes Found</h3>
            <p className="text-sm text-muted-foreground">
              "{movieTitle}" is not currently playing at nearby AMC theaters
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const theaterGroups = groupShowtimesByTheater(showtimes._embedded.showtimes)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Theater Showtimes
          </div>
          <Badge variant="secondary">
            {theaterGroups.length} theater{theaterGroups.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {theaterGroups.slice(0, 5).map((theaterGroup) => (
            <div key={theaterGroup.theaterId} className="space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium">{getTheaterName(theaterGroup.theaterId)}</h4>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {getTheaterShortAddress(theaterGroup.theaterId)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {theaterGroup.showtimes.length} showtime{theaterGroup.showtimes.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                {theaterGroup.showtimes.slice(0, 12).map((showtime) => (
                  <Button
                    key={showtime.id}
                    variant="outline"
                    size="sm"
                    className="h-auto p-2 text-xs flex-col items-start"
                    asChild
                  >
                    <a
                      href={showtime.purchaseUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full"
                    >
                      <div className="flex items-center justify-between w-full">
                        <span className="font-medium">
                          {formatTime(showtime.showDateTimeLocal)}
                        </span>
                        <ExternalLink className="w-2 h-2" />
                      </div>
                      {showtime.premiumFormat && (
                        <span className="text-[10px] text-muted-foreground mt-1">
                          {showtime.premiumFormat}
                        </span>
                      )}
                      {showtime.isDiscountMatineePriced && showtime.discountMatineeMessage && (
                        <span className="text-[10px] text-green-600 mt-1">
                          {showtime.discountMatineeMessage}
                        </span>
                      )}
                    </a>
                  </Button>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Showtimes provided by{' '}
            <a 
              href="https://www.amctheatres.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              AMC Theatres
            </a>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
