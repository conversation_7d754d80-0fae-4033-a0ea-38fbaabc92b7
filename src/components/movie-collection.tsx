'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TMDbCollection, tmdbClient } from '@/lib/tmdb'
import { Film } from 'lucide-react'
import Image from 'next/image'

interface MovieCollectionProps {
  collection: TMDbCollection
}

export function MovieCollection({ collection }: MovieCollectionProps) {
  if (!collection) {
    return null
  }

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Film className="h-5 w-5" />
          Part of Collection
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="flex">
          {/* Collection Poster */}
          <div className="flex-shrink-0 w-24 h-36 relative overflow-hidden">
            <Image
              src={tmdbClient.getImageUrl(collection.poster_path)}
              alt={collection.name}
              fill
              className="object-cover"
            />
          </div>
          
          {/* Collection Info */}
          <div className="flex-1 p-4 flex flex-col justify-center">
            <h4 className="font-semibold text-base mb-2 line-clamp-2">
              {collection.name}
            </h4>
            <Badge variant="outline" className="w-fit">
              Collection
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
