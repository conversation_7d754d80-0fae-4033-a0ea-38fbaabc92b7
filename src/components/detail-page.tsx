'use client'

import { useQuery } from '@tanstack/react-query'
import { tmdbClient } from '@/lib/tmdb'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Heart, Star, Plus, Share2, ArrowLeft, Calendar, Clock, Users } from 'lucide-react'
import { toast } from 'sonner'
import Image from 'next/image'
import Link from 'next/link'
import { TrailerPlayer } from './trailer-player'
import { ReviewsSection } from './reviews-section'
import { SimilarContent } from './similar-content'
import { WatchProviders } from './watch-providers'
import { MovieShowtimes } from './movie-showtimes'
import { MovieCollection } from './movie-collection'
import { Recommendations } from './recommendations'

interface DetailPageProps {
  type: 'movie' | 'tv'
  id: number
}

export function DetailPage({ type, id }: DetailPageProps) {
  const {
    data: details,
    isLoading: detailsLoading,
    error: detailsError,
  } = useQuery({
    queryKey: ['details', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieDetails(id)
      } else {
        return tmdbClient.getTVDetails(id)
      }
    },
  })

  const {
    data: credits,
    isLoading: creditsLoading,
  } = useQuery({
    queryKey: ['credits', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieCredits(id)
      } else {
        return tmdbClient.getTVCredits(id)
      }
    },
  })

  const {
    data: similar,
    isLoading: similarLoading,
  } = useQuery({
    queryKey: ['similar', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getSimilarMovies(id)
      } else {
        return tmdbClient.getSimilarTV(id)
      }
    },
  })

  const {
    data: videos,
    isLoading: videosLoading,
  } = useQuery({
    queryKey: ['videos', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieVideos(id)
      } else {
        return tmdbClient.getTVVideos(id)
      }
    },
  })

  const {
    data: reviews,
    isLoading: reviewsLoading,
  } = useQuery({
    queryKey: ['reviews', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieReviews(id)
      } else {
        return tmdbClient.getTVReviews(id)
      }
    },
  })

  const {
    data: watchProviders,
    isLoading: watchProvidersLoading,
  } = useQuery({
    queryKey: ['watchProviders', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieWatchProviders(id)
      } else {
        return tmdbClient.getTVWatchProviders(id)
      }
    },
  })

  const {
    data: recommendations,
    isLoading: recommendationsLoading,
  } = useQuery({
    queryKey: ['recommendations', type, id],
    queryFn: () => {
      if (type === 'movie') {
        return tmdbClient.getMovieRecommendations(id)
      } else {
        return tmdbClient.getTVRecommendations(id)
      }
    },
  })

  if (detailsError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Oops! Something went wrong</h2>
          <p className="text-muted-foreground mb-4">
            Could not load {type} details. Please try again.
          </p>
          <Link href="/">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Feed
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  if (detailsLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <Skeleton className="h-8 w-32" />
          <div className="relative h-96 w-full">
            <Skeleton className="h-full w-full rounded-lg" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!details) return null

  const title = type === 'movie' ? details.title : details.name
  const releaseDate = type === 'movie' ? details.release_date : details.first_air_date
  const year = releaseDate ? new Date(releaseDate).getFullYear() : 'TBA'
  
  const handleLike = () => {
    toast.success('Added to liked!')
  }

  const handleRate = () => {
    toast.success('Rating saved!')
  }

  const handleWatchlist = () => {
    toast.success('Added to watchlist!')
  }

  const handleRecommend = () => {
    toast.success('Recommendation shared!')
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="container mx-auto px-4 py-4">
        <Link href="/">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Feed
          </Button>
        </Link>
      </div>

      {/* Backdrop */}
      {details.backdrop_path && (
        <div className="relative h-96 w-full overflow-hidden">
          <Image
            src={tmdbClient.getBackdropUrl(details.backdrop_path)}
            alt={title}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title and Meta */}
            <div className="space-y-4">
              <div className="flex flex-wrap items-center gap-2">
                <Badge variant="secondary">
                  {type.toUpperCase()}
                </Badge>
                <span className="text-sm text-muted-foreground">{year}</span>
                {details.vote_average > 0 && (
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                    <span className="text-sm font-medium">
                      {details.vote_average.toFixed(1)}
                    </span>
                  </div>
                )}
              </div>
              
              <h1 className="text-4xl font-bold">{title}</h1>
              
              {details.tagline && (
                <p className="text-lg text-muted-foreground italic">
                  {details.tagline}
                </p>
              )}
            </div>

            {/* Overview */}
            <div className="space-y-2">
              <h2 className="text-xl font-semibold">Overview</h2>
              <p className="text-muted-foreground leading-relaxed">
                {details.overview || 'No overview available.'}
              </p>
            </div>

            {/* Details */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  Release
                </div>
                <p className="font-medium">
                  {releaseDate ? new Date(releaseDate).toLocaleDateString() : 'TBA'}
                </p>
              </div>
              
              {type === 'movie' && 'runtime' in details && details.runtime > 0 && (
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    Runtime
                  </div>
                  <p className="font-medium">{details.runtime} min</p>
                </div>
              )}
              
              {type === 'tv' && 'number_of_seasons' in details && (
                <>
                  <div className="space-y-1">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      Seasons
                    </div>
                    <p className="font-medium">{details.number_of_seasons}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      Episodes
                    </div>
                    <p className="font-medium">{details.number_of_episodes}</p>
                  </div>
                </>
              )}
              
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Star className="h-4 w-4" />
                  Rating
                </div>
                <p className="font-medium">
                  {details.vote_average.toFixed(1)}/10 ({details.vote_count} votes)
                </p>
              </div>
            </div>

            {/* Genres */}
            {details.genres && details.genres.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Genres</h3>
                <div className="flex flex-wrap gap-2">
                  {details.genres.map((genre) => (
                    <Badge key={genre.id} variant="outline">
                      {genre.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Cast */}
            {credits && credits.cast.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Cast</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {credits.cast.slice(0, 8).map((actor) => (
                    <Card key={actor.id} className="overflow-hidden">
                      <div className="aspect-[3/4] relative">
                        <Image
                          src={tmdbClient.getImageUrl(actor.profile_path)}
                          alt={actor.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <CardContent className="p-3">
                        <p className="font-medium text-sm">{actor.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {actor.character}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Trailers & Videos */}
            {videos && videos.results.length > 0 && (
              <TrailerPlayer videos={videos.results} title={title} />
            )}

            {/* Reviews */}
            <ReviewsSection
              reviews={reviews?.results || []}
              isLoading={reviewsLoading}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Poster */}
            <Card className="overflow-hidden">
              <div className="aspect-[2/3] relative">
                <Image
                  src={tmdbClient.getImageUrl(details.poster_path)}
                  alt={title}
                  fill
                  className="object-cover"
                />
              </div>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              <Button onClick={handleLike} variant="outline" className="w-full">
                <Heart className="mr-2 h-4 w-4" />
                Like
              </Button>
              <Button onClick={handleRate} variant="outline" className="w-full">
                <Star className="mr-2 h-4 w-4" />
                Rate
              </Button>
              <Button onClick={handleWatchlist} variant="outline" className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Watchlist
              </Button>
              <Button onClick={handleRecommend} variant="outline" className="w-full">
                <Share2 className="mr-2 h-4 w-4" />
                Recommend
              </Button>
            </div>

            {/* Movie Collection */}
            {type === 'movie' && 'belongs_to_collection' in details && details.belongs_to_collection && (
              <MovieCollection collection={details.belongs_to_collection} />
            )}

            {/* Watch Providers */}
            {watchProviders && (
              <WatchProviders
                watchProviders={watchProviders}
                isLoading={watchProvidersLoading}
                title={title}
              />
            )}

            {/* Movie Showtimes */}
            <MovieShowtimes
              movieTitle={title}
              isMovie={type === 'movie'}
            />
          </div>
        </div>

        {/* Similar Content */}
        {similar && similar.results.length > 0 && (
          <div className="mt-12">
            <SimilarContent
              content={similar.results}
              type={type}
              isLoading={similarLoading}
            />
          </div>
        )}

        {/* Recommendations */}
        <div className="mt-12">
          <Recommendations
            recommendations={recommendations?.results || []}
            type={type}
            isLoading={recommendationsLoading}
          />
        </div>
      </div>
    </div>
  )
}
