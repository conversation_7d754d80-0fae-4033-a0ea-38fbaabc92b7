'use client'

import Link from 'next/link'
import { Film, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { UserMenu } from './user-menu'

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Film className="h-6 w-6" />
          <span className="font-bold text-xl">WatchIt</span>
        </Link>

        {/* Navigation */}
        <nav className="flex items-center space-x-6 text-sm font-medium ml-6">
          <Link
            href="/"
            className="transition-colors hover:text-foreground/80 text-foreground"
          >
            Home
          </Link>
          <Link
            href="/movies"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
          >
            Movies
          </Link>
          <Link
            href="/tv"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
          >
            TV Shows
          </Link>
          <Link
            href="/browse"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
          >
            Browse
          </Link>
        </nav>

        {/* Search and User Menu */}
        <div className="flex flex-1 items-center justify-end space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search movies, TV shows..."
              className="pl-8 w-[300px] lg:w-[400px]"
            />
          </div>

          {/* Mobile Search Button */}
          <Button variant="ghost" size="sm" className="md:hidden">
            <Search className="h-4 w-4" />
          </Button>

          {/* User Menu */}
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
