import { NextRequest, NextResponse } from 'next/server'

const AMC_BASE_URL = 'https://api.amctheatres.com/v2'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const theaterId = searchParams.get('id')
  const theaterIds = searchParams.get('ids')

  if (!theaterId && !theaterIds) {
    return NextResponse.json(
      { error: 'Theater ID or IDs are required' },
      { status: 400 }
    )
  }

  const apiKey = process.env.NEXT_PUBLIC_AMC_API_KEY

  if (!apiKey) {
    return NextResponse.json(
      { error: 'AMC API key not configured' },
      { status: 500 }
    )
  }

  try {
    let url: string

    if (theaterIds) {
      // Multiple theaters: /theatres?ids=288,270,278
      url = `${AMC_BASE_URL}/theatres?ids=${encodeURIComponent(theaterIds)}`
    } else {
      // Single theater: /theatres/288
      url = `${AMC_BASE_URL}/theatres/${theaterId}`
    }

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'X-AMC-Vendor-Key': apiKey,
      }
    })

    if (!response.ok) {
      throw new Error(`AMC API error: ${response.statusText}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching AMC theater(s):', error)
    return NextResponse.json(
      { error: 'Failed to fetch theater(s) from AMC' },
      { status: 500 }
    )
  }
}
