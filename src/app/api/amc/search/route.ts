import { NextRequest, NextResponse } from 'next/server'

const AMC_BASE_URL = 'https://api.amctheatres.com/v2'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const movieName = searchParams.get('name')

  if (!movieName) {
    return NextResponse.json(
      { error: 'Movie name is required' },
      { status: 400 }
    )
  }

  const apiKey = process.env.NEXT_PUBLIC_AMC_API_KEY

  if (!apiKey) {
    return NextResponse.json(
      { error: 'AMC API key not configured' },
      { status: 500 }
    )
  }

  try {
    const encodedName = encodeURIComponent(movieName)
    const url = `${AMC_BASE_URL}/movies?name=${encodedName}`
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'X-AMC-Vendor-Key': apiKey,
      }
    })

    if (!response.ok) {
      throw new Error(`AMC API error: ${response.statusText}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching AMC movies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch movies from AMC' },
      { status: 500 }
    )
  }
}
