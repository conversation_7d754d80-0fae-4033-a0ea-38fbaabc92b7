import { NextRequest, NextResponse } from 'next/server'

const AMC_BASE_URL = 'https://api.amctheatres.com/v2'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const movieId = searchParams.get('movieId')
  const latitude = searchParams.get('latitude')
  const longitude = searchParams.get('longitude')
  const date = searchParams.get('date')

  if (!movieId || !latitude || !longitude) {
    return NextResponse.json(
      { error: 'Movie ID, latitude, and longitude are required' },
      { status: 400 }
    )
  }

  const apiKey = process.env.NEXT_PUBLIC_AMC_API_KEY

  if (!apiKey) {
    return NextResponse.json(
      { error: 'AMC API key not configured' },
      { status: 500 }
    )
  }

  try {
    // Format date as MM-DD-YYYY (default to today)
    const targetDate = date || new Date().toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    }).replace(/\//g, '-')

    const url = `${AMC_BASE_URL}/showtimes/views/current-location/${targetDate}/${latitude}/${longitude}?movie-id=${movieId}`
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'X-AMC-Vendor-Key': apiKey,
      }
    })

    if (!response.ok) {
      throw new Error(`AMC API error: ${response.statusText}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching AMC showtimes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch showtimes from AMC' },
      { status: 500 }
    )
  }
}
