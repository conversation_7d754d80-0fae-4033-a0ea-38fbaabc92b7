'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TitleCard } from '@/components/title-card'
import { CreateListModal } from '@/components/create-list-modal'
import { watchlistService } from '@/lib/watchlist'
import { useAuth } from '@/lib/auth-context'
import { toast } from 'sonner'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Globe, 
  Lock, 
  Calendar,
  Film,
  Tv,
  Plus
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

export default function ListDetailPage() {
  const params = useParams()
  const router = useRouter()
  const listId = params.id as string
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const [editModalOpen, setEditModalOpen] = useState(false)

  // Get list details
  const { data: userLists = [] } = useQuery({
    queryKey: ['user-lists', user?.id],
    queryFn: () => user ? watchlistService.getUserLists(user.id) : [],
    enabled: !!user,
  })

  const list = userLists.find(l => l.id === listId)

  // Get list items
  const { data: listItems = [], isLoading: itemsLoading } = useQuery({
    queryKey: ['list-items', listId],
    queryFn: () => watchlistService.getListItems(listId),
    enabled: !!listId,
  })

  // Delete list mutation
  const deleteListMutation = useMutation({
    mutationFn: () => watchlistService.deleteList(listId),
    onSuccess: (success) => {
      if (success) {
        toast.success('List deleted successfully')
        queryClient.invalidateQueries({ queryKey: ['user-lists', user?.id] })
        router.push('/lists')
      } else {
        toast.error('Failed to delete list')
      }
    },
    onError: () => {
      toast.error('Failed to delete list')
    }
  })

  const handleDeleteList = () => {
    if (confirm('Are you sure you want to delete this list? This action cannot be undone.')) {
      deleteListMutation.mutate()
    }
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">List Not Found</h1>
          <p className="text-muted-foreground">Please sign in to view this list.</p>
        </div>
      </div>
    )
  }

  if (!list) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">List Not Found</h1>
          <p className="text-muted-foreground">This list doesn't exist or you don't have access to it.</p>
          <Link href="/lists">
            <Button className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Lists
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/lists">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h1 className="text-3xl font-bold">{list.name}</h1>
            {list.is_public ? (
              <Globe className="h-5 w-5 text-green-600" />
            ) : (
              <Lock className="h-5 w-5 text-muted-foreground" />
            )}
            {list.is_default && (
              <Badge variant="secondary">Default</Badge>
            )}
          </div>
          {list.description && (
            <p className="text-muted-foreground">{list.description}</p>
          )}
          <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Created {new Date(list.created_at).toLocaleDateString()}
            </span>
            <span>{listItems.length} items</span>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setEditModalOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          {!list.is_default && (
            <Button 
              variant="outline" 
              onClick={handleDeleteList}
              disabled={deleteListMutation.isPending}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* List Items */}
      {itemsLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {[...Array(10)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-[2/3] bg-muted rounded-t-lg"></div>
              <CardContent className="p-4">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : listItems.length === 0 ? (
        <div className="text-center py-12">
          <Film className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">No items in this list yet</h2>
          <p className="text-muted-foreground mb-6">
            Start adding movies and TV shows to build your collection
          </p>
          <Link href="/">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Browse Movies & TV
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {listItems.map((item) => {
            // Convert to TitleCard format
            const tmdbData = {
              id: parseInt(item.source_id),
              media_type: item.content_type as 'movie' | 'tv',
              title: item.content_type === 'movie' ? item.title : undefined,
              name: item.content_type === 'tv' ? item.title : undefined,
              poster_path: item.poster_url?.replace('https://image.tmdb.org/t/p/w500', ''),
              backdrop_path: item.backdrop_url?.replace('https://image.tmdb.org/t/p/w1280', ''),
              overview: item.overview,
              release_date: item.content_type === 'movie' && item.year ? `${item.year}-01-01` : undefined,
              first_air_date: item.content_type === 'tv' && item.year ? `${item.year}-01-01` : undefined,
            }

            return (
              <TitleCard
                key={item.id}
                id={`${item.content_type}-${item.source_id}`}
                title={item.title}
                year={item.year || 0}
                posterUrl={item.poster_url || ''}
                contentType={item.content_type}
                tmdbData={tmdbData}
              />
            )
          })}
        </div>
      )}

      {/* Edit List Modal */}
      <CreateListModal
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
      />
    </div>
  )
}
