'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CreateListModal } from '@/components/create-list-modal'
import { UserAvatar } from '@/components/user-avatar'
import { watchlistService } from '@/lib/watchlist'
import { useAuth } from '@/lib/auth-context'
import { Plus, List as ListIcon, Lock, Globe, Trash2, Edit } from 'lucide-react'
import Link from 'next/link'

export default function ListsPage() {
  const [createListOpen, setCreateListOpen] = useState(false)
  const { user, profile } = useAuth()

  const { data: userLists = [], isLoading } = useQuery({
    queryKey: ['user-lists', user?.id],
    queryFn: () => user ? watchlistService.getUserLists(user.id) : [],
    enabled: !!user,
  })

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">My Lists</h1>
          <p className="text-muted-foreground">Please sign in to view your lists.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <UserAvatar
            userId={user.id}
            displayName={profile?.display_name}
            username={profile?.username}
            email={user.email}
            avatarUrl={profile?.avatar_url}
            size="lg"
          />
          <div>
            <h1 className="text-3xl font-bold">My Lists</h1>
            <p className="text-muted-foreground">
              Organize your movies and TV shows into custom collections
            </p>
          </div>
        </div>
        <Button onClick={() => setCreateListOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create List
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-muted rounded w-3/4"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded w-full mb-2"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : userLists.length === 0 ? (
        <div className="text-center py-12">
          <ListIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">No lists yet</h2>
          <p className="text-muted-foreground mb-6">
            Create your first list to start organizing your favorite movies and TV shows
          </p>
          <Button onClick={() => setCreateListOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Your First List
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userLists.map((list) => (
            <Card key={list.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="flex items-center gap-2">
                      <Link 
                        href={`/lists/${list.id}`}
                        className="hover:underline"
                      >
                        {list.name}
                      </Link>
                      {list.is_public ? (
                        <Globe className="h-4 w-4 text-green-600" />
                      ) : (
                        <Lock className="h-4 w-4 text-muted-foreground" />
                      )}
                    </CardTitle>
                    {list.is_default && (
                      <div className="text-xs text-blue-600 font-medium">
                        Default Watchlist
                      </div>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    {!list.is_default && (
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {list.description && (
                  <p className="text-muted-foreground text-sm mb-3">
                    {list.description}
                  </p>
                )}
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Created {new Date(list.created_at).toLocaleDateString()}
                  </span>
                  <Link 
                    href={`/lists/${list.id}`}
                    className="text-primary hover:underline"
                  >
                    View List →
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <CreateListModal
        open={createListOpen}
        onOpenChange={setCreateListOpen}
      />
    </div>
  )
}
