import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextRequest } from 'next/server'
import { redirect } from 'next/navigation'

export default async function AuthCallbackPage({
  searchParams,
}: {
  searchParams: { code?: string; error?: string }
}) {
  const supabase = createServerSupabaseClient()

  if (searchParams.error) {
    // Handle OAuth error
    redirect('/?error=' + encodeURIComponent(searchParams.error))
  }

  if (searchParams.code) {
    // Exchange the code for a session
    const { error } = await supabase.auth.exchangeCodeForSession(searchParams.code)
    
    if (error) {
      redirect('/?error=' + encodeURIComponent(error.message))
    }
  }

  // Successful authentication, redirect to home
  redirect('/')
}
