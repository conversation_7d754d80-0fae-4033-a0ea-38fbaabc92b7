'use client'

import { useState, useEffect } from 'react'

export interface GeolocationState {
  latitude: number | null
  longitude: number | null
  error: string | null
  loading: boolean
  permission: 'granted' | 'denied' | 'prompt' | null
}

export function useGeolocation() {
  const [state, setState] = useState<GeolocationState>({
    latitude: null,
    longitude: null,
    error: null,
    loading: false,
    permission: null
  })

  const requestLocation = async () => {
    if (!navigator.geolocation) {
      setState(prev => ({
        ...prev,
        error: 'Geolocation is not supported by this browser',
        loading: false
      }))
      return
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      // Check current permission status
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'geolocation' })
        setState(prev => ({ ...prev, permission: permission.state }))
        
        if (permission.state === 'denied') {
          setState(prev => ({
            ...prev,
            error: 'Location access denied. Please enable location permissions to see theater showtimes.',
            loading: false
          }))
          return
        }
      }

      // Request current position
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setState({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            error: null,
            loading: false,
            permission: 'granted'
          })
        },
        (error) => {
          let errorMessage = 'Unable to retrieve location'
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied. Please enable location permissions to see theater showtimes.'
              setState(prev => ({ ...prev, permission: 'denied' }))
              break
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable'
              break
            case error.TIMEOUT:
              errorMessage = 'Location request timed out'
              break
          }
          
          setState(prev => ({
            ...prev,
            error: errorMessage,
            loading: false
          }))
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      )
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to request location permission',
        loading: false
      }))
    }
  }

  return {
    ...state,
    requestLocation
  }
}
