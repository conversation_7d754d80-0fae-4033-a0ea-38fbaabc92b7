/**
 * Utility functions for generating consistent user avatars
 */

// Predefined color palette for default avatars
const AVATAR_COLORS = [
  'bg-red-500',
  'bg-blue-500', 
  'bg-green-500',
  'bg-yellow-500',
  'bg-purple-500',
  'bg-pink-500',
  'bg-indigo-500',
  'bg-teal-500',
  'bg-orange-500',
  'bg-cyan-500',
  'bg-lime-500',
  'bg-emerald-500',
  'bg-rose-500',
  'bg-violet-500',
  'bg-amber-500',
  'bg-sky-500'
] as const

/**
 * Generate a consistent color class based on a string (usually user ID)
 */
export function getAvatarColor(seed: string): string {
  const hash = seed.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  const colorIndex = hash % AVATAR_COLORS.length
  return AVATAR_COLORS[colorIndex]
}

/**
 * Generate initials from a display name
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

/**
 * Get a fallback display name from user data
 */
export function getDisplayName(
  displayName?: string | null,
  username?: string | null,
  email?: string | null
): string {
  if (displayName?.trim()) return displayName.trim()
  if (username?.trim()) return username.trim()
  if (email) return email.split('@')[0]
  return 'User'
}

/**
 * Generate avatar props for consistent styling
 */
export function getAvatarProps(
  userId: string,
  displayName?: string | null,
  username?: string | null,
  email?: string | null,
  avatarUrl?: string | null
) {
  const name = getDisplayName(displayName, username, email)
  const initials = getInitials(name)
  const colorClass = getAvatarColor(userId)

  return {
    name,
    initials,
    colorClass,
    avatarUrl: avatarUrl || undefined,
    fallbackProps: {
      className: `${colorClass} text-white font-semibold`,
      children: initials
    }
  }
}
