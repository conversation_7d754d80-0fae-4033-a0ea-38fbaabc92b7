const AMC_API_BASE_URL = '/api/amc'

export interface AMCMovie {
  id: number
  name: string
  slug: string
  mpaaRating: string
  runTime: number
  genre: string
  synopsis: string
  releaseDateUtc: string
  hasScheduledShowtimes: boolean
  websiteUrl: string
  showtimesUrl: string
  media: {
    posterDynamic: string
    trailerHd?: string
    trailerMp4?: string
  }
}

export interface AMCMoviesResponse {
  _embedded: {
    movies: AMCMovie[]
  }
  count: number
  pageSize: number
  pageNumber: number
}

export interface AMCShowtime {
  id: number
  movieId: number
  movieName: string
  showDateTimeUtc: string
  showDateTimeLocal: string
  theatreId: number
  auditorium: number
  runTime: number
  mpaaRating: string
  premiumFormat?: string
  purchaseUrl: string
  mobilePurchaseUrl: string
  isSoldOut: boolean
  isAlmostSoldOut: boolean
  isCanceled: boolean
  isDiscountMatineePriced: boolean
  discountMatineeMessage?: string
}

export interface AMCShowtimesResponse {
  _embedded: {
    showtimes: AMCShowtime[]
  }
  count: number
  pageSize: number
  pageNumber: number
  lastUpdatedDateUtc: string
}

export interface AMCTheater {
  id: number
  name: string
  slug: string
  location: {
    addressLine1: string
    addressLine2?: string
    city: string
    state: string
    postalCode: string
    latitude: number
    longitude: number
    directionsUrl: string
  }
  guestServicesPhoneNumber: string
}

export interface AMCTheatersResponse {
  _embedded: {
    theatres: AMCTheater[]
  }
  count: number
}

class AMCClient {
  private async request<T>(endpoint: string): Promise<T> {
    const url = `${AMC_API_BASE_URL}${endpoint}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`AMC API error: ${response.statusText}`)
    }

    return response.json()
  }

  async searchMovies(movieName: string): Promise<AMCMoviesResponse> {
    const encodedName = encodeURIComponent(movieName)
    return this.request(`/search?name=${encodedName}`)
  }

  async getShowtimes(
    movieId: number,
    latitude: number,
    longitude: number,
    date?: string
  ): Promise<AMCShowtimesResponse> {
    const params = new URLSearchParams({
      movieId: movieId.toString(),
      latitude: latitude.toString(),
      longitude: longitude.toString(),
    })

    if (date) {
      params.append('date', date)
    }

    return this.request(`/showtimes?${params.toString()}`)
  }

  async getTheater(theaterId: number): Promise<any> {
    return this.request(`/theater?id=${theaterId}`)
  }

  async getTheaters(theaterIds: number[]): Promise<any> {
    const ids = theaterIds.join(',')
    return this.request(`/theater?ids=${ids}`)
  }

  // Helper method to find AMC movie by TMDB title
  async findMovieByTitle(title: string): Promise<AMCMovie | null> {
    try {
      const response = await this.searchMovies(title)
      const movies = response._embedded?.movies || []

      // Try exact match first
      let movie = movies.find(m =>
        m.name.toLowerCase() === title.toLowerCase()
      )

      // If no exact match, try partial match
      if (!movie) {
        movie = movies.find(m =>
          m.name.toLowerCase().includes(title.toLowerCase()) ||
          title.toLowerCase().includes(m.name.toLowerCase())
        )
      }

      return movie || null
    } catch (error) {
      console.error('Error searching AMC movies:', error)
      return null
    }
  }

  // Get showtimes for a movie title with location
  async getShowtimesByTitle(
    title: string,
    latitude: number,
    longitude: number,
    date?: string
  ): Promise<AMCShowtimesResponse | null> {
    try {
      const movie = await this.findMovieByTitle(title)
      if (!movie) {
        return null
      }

      return await this.getShowtimes(movie.id, latitude, longitude, date)
    } catch (error) {
      console.error('Error getting AMC showtimes:', error)
      return null
    }
  }
}

export const amcClient = new AMCClient()
