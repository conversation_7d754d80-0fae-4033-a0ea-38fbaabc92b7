import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const createClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Legacy export for backward compatibility
export const supabase = createClient()

// Database Types
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: Profile
        Insert: Omit<Profile, 'created_at' | 'updated_at'>
        Update: Partial<Omit<Profile, 'user_id' | 'created_at' | 'updated_at'>>
      }
      titles: {
        Row: Title
        Insert: Omit<Title, 'id' | 'created_at'>
        Update: Partial<Omit<Title, 'id' | 'created_at'>>
      }
      title_actions: {
        Row: TitleAction
        Insert: Omit<TitleAction, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<TitleAction, 'id' | 'user_id' | 'title_id' | 'created_at' | 'updated_at'>>
      }
      lists: {
        Row: List
        Insert: Omit<List, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<List, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
      }
      list_items: {
        Row: ListItem
        Insert: Omit<ListItem, 'id' | 'added_at'>
        Update: Partial<Omit<ListItem, 'id' | 'list_id' | 'title_id' | 'added_at'>>
      }
      reviews: {
        Row: Review
        Insert: Omit<Review, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Review, 'id' | 'user_id' | 'title_id' | 'created_at' | 'updated_at'>>
      }
      follows: {
        Row: Follow
        Insert: Omit<Follow, 'id' | 'created_at'>
        Update: never
      }
    }
  }
}

export type Profile = {
  user_id: string
  username: string | null
  display_name: string | null
  avatar_url: string | null
  bio: string | null
  created_at: string
  updated_at: string
}

export type Title = {
  id: string
  content_type: 'movie' | 'tv' | 'anime'
  source: 'tmdb' | 'anilist'
  source_id: string
  title: string
  year: number | null
  poster_url: string | null
  backdrop_url: string | null
  overview: string | null
  metadata: any
  created_at: string
}

export type TitleAction = {
  id: string
  user_id: string
  title_id: string
  liked: boolean
  rating: number | null
  is_watchlisted: boolean
  is_recommended: boolean
  note: string | null
  created_at: string
  updated_at: string
}

export type List = {
  id: string
  user_id: string
  name: string
  description: string | null
  is_public: boolean
  is_default: boolean
  created_at: string
  updated_at: string
}

export type ListItem = {
  id: string
  list_id: string
  title_id: string
  position: number
  added_at: string
}

export type Review = {
  id: string
  user_id: string
  title_id: string
  rating: number
  review_text: string | null
  is_spoiler: boolean
  created_at: string
  updated_at: string
}

export type Follow = {
  id: string
  follower_id: string
  following_id: string
  created_at: string
}
