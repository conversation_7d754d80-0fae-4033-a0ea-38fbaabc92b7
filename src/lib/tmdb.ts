const TMDB_BASE_URL = 'https://api.themoviedb.org/3'
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p/w500'

export interface TMDbMovie {
  id: number
  title: string
  poster_path: string | null
  release_date: string
  overview: string
  vote_average: number
  genre_ids: number[]
}

export interface TMDbTVShow {
  id: number
  name: string
  poster_path: string | null
  first_air_date: string
  overview: string
  vote_average: number
  genre_ids: number[]
}

export interface TMDbResponse<T> {
  page: number
  results: T[]
  total_pages: number
  total_results: number
}

export interface TMDbCollection {
  id: number
  name: string
  poster_path: string | null
  backdrop_path: string | null
}

export interface TMDbMovieDetails {
  id: number
  title: string
  tagline: string
  overview: string
  poster_path: string | null
  backdrop_path: string | null
  release_date: string
  runtime: number
  vote_average: number
  vote_count: number
  genres: { id: number; name: string }[]
  production_companies: { id: number; name: string; logo_path: string | null }[]
  budget: number
  revenue: number
  belongs_to_collection: TMDbCollection | null
}

export interface TMDbTVDetails {
  id: number
  name: string
  tagline: string
  overview: string
  poster_path: string | null
  backdrop_path: string | null
  first_air_date: string
  last_air_date: string
  number_of_episodes: number
  number_of_seasons: number
  vote_average: number
  vote_count: number
  genres: { id: number; name: string }[]
  production_companies: { id: number; name: string; logo_path: string | null }[]
  created_by: { id: number; name: string }[]
  episode_run_time: number[]
}

export interface TMDbCastMember {
  id: number
  name: string
  character: string
  profile_path: string | null
  order: number
}

export interface TMDbCredits {
  cast: TMDbCastMember[]
}

export interface TMDbVideo {
  id: string
  key: string
  name: string
  site: string
  type: string
  official: boolean
  published_at: string
}

export interface TMDbVideos {
  results: TMDbVideo[]
}

export interface TMDbReview {
  id: string
  author: string
  author_details: {
    name: string
    username: string
    avatar_path: string | null
    rating: number | null
  }
  content: string
  created_at: string
  updated_at: string
  url: string
}

export interface TMDbReviews {
  page: number
  results: TMDbReview[]
  total_pages: number
  total_results: number
}

export interface TMDbWatchProvider {
  logo_path: string
  provider_id: number
  provider_name: string
  display_priority: number
}

export interface TMDbCountryWatchProviders {
  link: string
  flatrate?: TMDbWatchProvider[]
  rent?: TMDbWatchProvider[]
  buy?: TMDbWatchProvider[]
  ads?: TMDbWatchProvider[]
  free?: TMDbWatchProvider[]
}

export interface TMDbWatchProviders {
  id: number
  results: Record<string, TMDbCountryWatchProviders>
}

class TMDbClient {
  private apiKey: string

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_TMDB_API_KEY || ''
  }

  private async request<T>(endpoint: string): Promise<T> {
    const url = `${TMDB_BASE_URL}${endpoint}`
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`TMDb API error: ${response.statusText}`)
    }

    return response.json()
  }

  async getTrendingMovies(page = 1): Promise<TMDbResponse<TMDbMovie>> {
    return this.request(`/trending/movie/week?page=${page}`)
  }

  async getTrendingTV(page = 1): Promise<TMDbResponse<TMDbTVShow>> {
    return this.request(`/trending/tv/week?page=${page}`)
  }

  async getPopularMovies(page = 1): Promise<TMDbResponse<TMDbMovie>> {
    return this.request(`/movie/popular?page=${page}`)
  }

  async getPopularTV(page = 1): Promise<TMDbResponse<TMDbTVShow>> {
    return this.request(`/tv/popular?page=${page}`)
  }

  async searchMovies(query: string, page = 1): Promise<TMDbResponse<TMDbMovie>> {
    return this.request(`/search/movie?query=${encodeURIComponent(query)}&page=${page}`)
  }

  async searchTV(query: string, page = 1): Promise<TMDbResponse<TMDbTVShow>> {
    return this.request(`/search/tv?query=${encodeURIComponent(query)}&page=${page}`)
  }

  async getMovieDetails(movieId: number): Promise<TMDbMovieDetails> {
    return this.request(`/movie/${movieId}`)
  }

  async getTVDetails(tvId: number): Promise<TMDbTVDetails> {
    return this.request(`/tv/${tvId}`)
  }

  async getMovieCredits(movieId: number): Promise<TMDbCredits> {
    return this.request(`/movie/${movieId}/credits`)
  }

  async getTVCredits(tvId: number): Promise<TMDbCredits> {
    return this.request(`/tv/${tvId}/credits`)
  }

  async getSimilarMovies(movieId: number, page = 1): Promise<TMDbResponse<TMDbMovie>> {
    return this.request(`/movie/${movieId}/similar?page=${page}`)
  }

  async getSimilarTV(tvId: number, page = 1): Promise<TMDbResponse<TMDbTVShow>> {
    return this.request(`/tv/${tvId}/similar?page=${page}`)
  }

  async getMovieVideos(movieId: number): Promise<TMDbVideos> {
    return this.request(`/movie/${movieId}/videos`)
  }

  async getTVVideos(tvId: number): Promise<TMDbVideos> {
    return this.request(`/tv/${tvId}/videos`)
  }

  async getMovieReviews(movieId: number, page = 1): Promise<TMDbReviews> {
    return this.request(`/movie/${movieId}/reviews?page=${page}`)
  }

  async getTVReviews(tvId: number, page = 1): Promise<TMDbReviews> {
    return this.request(`/tv/${tvId}/reviews?page=${page}`)
  }

  async getMovieWatchProviders(movieId: number): Promise<TMDbWatchProviders> {
    return this.request(`/movie/${movieId}/watch/providers`)
  }

  async getTVWatchProviders(tvId: number): Promise<TMDbWatchProviders> {
    return this.request(`/tv/${tvId}/watch/providers`)
  }

  async getMovieRecommendations(movieId: number, page = 1): Promise<TMDbResponse<TMDbMovie>> {
    return this.request(`/movie/${movieId}/recommendations?page=${page}`)
  }

  async getTVRecommendations(tvId: number, page = 1): Promise<TMDbResponse<TMDbTVShow>> {
    return this.request(`/tv/${tvId}/recommendations?page=${page}`)
  }

  getImageUrl(path: string | null): string {
    if (!path) return 'https://via.placeholder.com/500x750/374151/f3f4f6?text=No+Image'
    return `${TMDB_IMAGE_BASE_URL}${path}`
  }

  getBackdropUrl(path: string | null): string {
    if (!path) return 'https://via.placeholder.com/1920x1080/374151/f3f4f6?text=No+Image'
    return `https://image.tmdb.org/t/p/w1280${path}`
  }
}

export const tmdbClient = new TMDbClient()
