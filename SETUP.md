# WatchIt Setup Guide

## 🎬 What We've Built

A modern movie and TV tracking app with:
- **Next.js 14** with App Router and TypeScript
- **shadcn/ui** components for beautiful UI
- **TanStack Query** for data fetching and caching
- **Tailwind CSS** for styling
- **Supabase** ready for auth and database
- **TMDb API** integration for movie/TV data

## 🚀 Current Status

✅ **COMPLETED:**
- Next.js project initialized with TypeScript
- shadcn/ui components installed and configured
- TanStack Query provider set up
- Main feed with infinite scroll (skeleton loading)
- Title cards with like, rate, watchlist, recommend actions
- TMDb API client ready
- Responsive design with tabs (Feed, Browse, Lists, Profile)

🔄 **NEXT STEPS:**
1. Get TMDb API key and add to `.env.local`
2. Set up Supabase project and database
3. Implement authentication
4. Build browse, lists, and profile pages
5. Add real data persistence

## 🔑 API Keys Needed

### 1. TMDb API Key
1. Go to [TMDb](https://www.themoviedb.org/settings/api)
2. Create an account and request API key
3. Add to `.env.local`:
   ```
   TMDB_API_KEY=your_api_key_here
   NEXT_PUBLIC_TMDB_API_KEY=your_api_key_here
   ```

### 2. Supabase Project
1. Go to [Supabase](https://supabase.com)
2. Create a new project
3. Get URL and anon key from Settings > API
4. Add to `.env.local`:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

## 🎯 Features Working Now

- **Responsive grid layout** with movie/TV cards
- **Infinite scroll** loading (shows skeletons without API key)
- **Interactive cards** with heart, star, plus, share buttons
- **Rating dialog** with 5-star system
- **Toast notifications** for user actions
- **Tab navigation** between Feed, Browse, Lists, Profile
- **Double-tap to like** on poster images
- **Modern UI** with dark/light mode support

## 🛠 Development

```bash
# Start development server
npm run dev

# Open http://localhost:3000
```

## 📱 App Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout with providers
│   └── page.tsx            # Home page (renders MainFeed)
├── components/
│   ├── main-feed.tsx       # Main feed with infinite scroll
│   ├── title-card.tsx      # Movie/TV card component
│   └── ui/                 # shadcn/ui components
├── lib/
│   ├── supabase.ts         # Supabase client and types
│   ├── tmdb.ts             # TMDb API client
│   ├── query-client.tsx    # TanStack Query provider
│   └── utils.ts            # Utility functions
```

## 🎨 Design System

Using **shadcn/ui** components:
- `Card` - Movie/TV cards
- `Button` - Action buttons
- `Badge` - Content type indicators
- `Dialog` - Rating modal
- `Toggle` - Like button
- `Tabs` - Navigation
- `Skeleton` - Loading states
- `Sonner` - Toast notifications

## 🔥 What Makes This Special

1. **Instagram-style feed** - Infinite scroll with mixed content
2. **Optimistic updates** - Instant feedback on user actions
3. **Modern stack** - Latest Next.js, React Query, Supabase
4. **Type-safe** - Full TypeScript coverage
5. **Responsive** - Works on mobile and desktop
6. **Accessible** - Built with accessibility in mind

Ready to add your API keys and see the magic happen! 🚀
